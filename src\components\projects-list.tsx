'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { toast } from 'sonner';
import { MoreVertical, Archive, Trash2, ExternalLink, Grid3X3, List } from 'lucide-react';
import { deleteProject, updateProject } from '@/services/project-service';
import { useRouter } from 'next/navigation';

interface ProjectsListProps {
  projects: any[];
}

export function ProjectsList({ projects }: ProjectsListProps) {
  const router = useRouter();
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isArchiving, setIsArchiving] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const menuRef = useRef<HTMLDivElement>(null);

  // Maintain local state of projects for real-time updates
  const [localProjects, setLocalProjects] = useState<any[]>([]);

  // Initialize local projects from props
  useEffect(() => {
    if (Array.isArray(projects)) {
      setLocalProjects(projects);
    }
  }, [projects]);

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setActiveMenu(null);
      }
    }

    if (activeMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeMenu]);

  // Make sure we have valid projects to display
  const validProjects = localProjects.length > 0 ? localProjects : (Array.isArray(projects) ? projects : []);

  // Handle delete project
  const handleDeleteProject = async (e: React.MouseEvent, projectId: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      setIsDeleting(projectId);
      try {
        const { success, error } = await deleteProject(projectId);

        if (success) {
          // Update local state immediately for real-time UI update
          setLocalProjects(prevProjects => prevProjects.filter(p => p.id !== projectId));
          toast.success('Project deleted successfully');

          // Still refresh the router to update other components
          router.refresh();
        } else {
          toast.error(`Failed to delete project: ${error?.message || 'Unknown error'}`);
        }
      } catch (err) {
        toast.error('An error occurred while deleting the project');
        console.error('Error deleting project:', err);
      } finally {
        setIsDeleting(null);
        setActiveMenu(null);
      }
    }
  };

  // Handle archive project
  const handleArchiveProject = async (e: React.MouseEvent, projectId: string, project: any) => {
    e.preventDefault();
    e.stopPropagation();

    setIsArchiving(projectId);
    try {
      const updatedProject = {
        ...project,
        status: 'Archived'
      };

      const { data, error } = await updateProject(projectId, updatedProject);

      if (data) {
        // Update local state immediately for real-time UI update
        setLocalProjects(prevProjects =>
          prevProjects.map(p => p.id === projectId ? { ...p, status: 'Archived' } : p)
        );

        toast.success('Project archived successfully');

        // Still refresh the router to update other components
        router.refresh();
      } else {
        toast.error(`Failed to archive project: ${error?.message || 'Unknown error'}`);
      }
    } catch (err) {
      toast.error('An error occurred while archiving the project');
      console.error('Error archiving project:', err);
    } finally {
      setIsArchiving(null);
      setActiveMenu(null);
    }
  };

  // Toggle menu for a project
  const toggleMenu = (e: React.MouseEvent, projectId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setActiveMenu(activeMenu === projectId ? null : projectId);
  };

  // Handle row click to navigate to project
  const handleRowClick = (projectId: string) => {
    router.push(`/projects/${projectId}`);
  };

  // Add sorting state
  const [sortField, setSortField] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Handle sort change
  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort projects
  const sortedProjects = [...validProjects].sort((a, b) => {
    const aValue = a[sortField] || '';
    const bValue = b[sortField] || '';

    if (sortDirection === 'asc') {
      return aValue.localeCompare(bValue);
    } else {
      return bValue.localeCompare(aValue);
    }
  });

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (e) {
      return dateString;
    }
  };

  // Function to get vibrant color based on project name
  const getProjectColor = (name: string) => {
    // Array of vibrant colors
    const colors = [
      { bg: '#e0f2fe', text: '#0284c7' }, // Sky blue
      { bg: '#fef3c7', text: '#d97706' }, // Amber
      { bg: '#dcfce7', text: '#16a34a' }, // Green
      { bg: '#f3e8ff', text: '#9333ea' }, // Purple
      { bg: '#ffedd5', text: '#ea580c' }, // Orange
      { bg: '#dbeafe', text: '#2563eb' }, // Blue
      { bg: '#fce7f3', text: '#db2777' }, // Pink
      { bg: '#d1fae5', text: '#059669' }, // Emerald
      { bg: '#ffe4e6', text: '#e11d48' }, // Rose
      { bg: '#f5f3ff', text: '#7c3aed' }  // Violet
    ];

    // Use the first character of the name to select a color
    const charCode = (name || 'A').charCodeAt(0);
    return colors[charCode % colors.length];
  };

  return (
    <div className="space-y-4">
      {/* View Toggle Controls */}
      <div className="flex items-center justify-between py-2">
        <div className="text-sm text-slate-500">
          {sortedProjects.length} {sortedProjects.length === 1 ? 'project' : 'projects'}
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-slate-600 mr-2">View:</span>
          <div className="flex bg-slate-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                viewMode === 'grid'
                  ? 'bg-white text-violet-600 shadow-sm'
                  : 'text-slate-600 hover:text-slate-800'
              }`}
            >
              <Grid3X3 className="w-4 h-4 mr-1.5" />
              Grid
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`flex items-center px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                viewMode === 'list'
                  ? 'bg-white text-violet-600 shadow-sm'
                  : 'text-slate-600 hover:text-slate-800'
              }`}
            >
              <List className="w-4 h-4 mr-1.5" />
              List
            </button>
          </div>
        </div>
      </div>

      {/* Conditional Rendering based on View Mode */}
      {viewMode === 'grid' ? (
        /* Grid View */
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {sortedProjects.map((project, index) => {
            // Get color based on project name
            const projectColor = getProjectColor(project.name || '');

            return (
              <div
                key={project.id}
                className="group relative bg-white rounded-lg border border-slate-300 shadow-sm hover:shadow-xl hover:shadow-slate-200/50 transition-all duration-300 cursor-pointer overflow-hidden"
                onClick={() => handleRowClick(project.id)}
              >
                {/* Card Header with Gradient */}
                <div
                  className="h-2 w-full"
                  style={{
                    background: `linear-gradient(90deg, ${projectColor.bg} 0%, ${projectColor.bg}aa 100%)`
                  }}
                />

                <div className="p-6">
                  {/* Project Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-12 h-12 rounded-xl flex items-center justify-center shadow-md"
                        style={{
                          background: `linear-gradient(135deg, ${projectColor.bg} 0%, ${projectColor.bg}dd 100%)`,
                          color: projectColor.text
                        }}
                      >
                        <span className="text-lg font-bold">
                          {(project.name || '').charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-slate-900 text-lg group-hover:text-violet-600 transition-colors leading-tight">
                          {project.name || 'Unnamed Project'}
                        </h3>
                        <div className="flex items-center text-sm text-slate-500 mt-1">
                          <LocationIcon className="w-4 h-4 mr-1.5 flex-shrink-0" />
                          <span className="truncate">{project.location || 'No location'}</span>
                        </div>
                      </div>
                    </div>

                    {/* Actions Menu */}
                    <div className="relative" onClick={(e) => e.stopPropagation()}>
                      <button
                        className="p-2 rounded-lg text-slate-400 hover:text-slate-600 hover:bg-slate-100 transition-all opacity-0 group-hover:opacity-100"
                        onClick={(e) => toggleMenu(e, project.id)}
                      >
                        <MoreVertical className="w-5 h-5" />
                      </button>

                      {/* Action Menu */}
                      {activeMenu === project.id && (
                        <div ref={menuRef} className="absolute top-full right-0 mt-2 z-30 bg-white rounded-lg shadow-xl py-2 min-w-[180px] border border-slate-300">
                          <Link
                            href={`/projects/${project.id}`}
                            className="flex items-center px-4 py-2.5 text-sm text-slate-700 hover:bg-slate-50 hover:text-violet-600 w-full text-left transition-colors"
                          >
                            <ExternalLink className="w-4 h-4 mr-3" />
                            View Project
                          </Link>
                          <button
                            className="flex items-center px-4 py-2.5 text-sm text-slate-700 hover:bg-slate-50 hover:text-violet-600 w-full text-left transition-colors"
                            onClick={(e) => handleArchiveProject(e, project.id, project)}
                            disabled={isArchiving === project.id}
                          >
                            <Archive className="w-4 h-4 mr-3" />
                            {isArchiving === project.id ? 'Archiving...' : 'Archive Project'}
                          </button>
                          <div className="border-t border-slate-100 my-1"></div>
                          <button
                            className="flex items-center px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 w-full text-left transition-colors"
                            onClick={(e) => handleDeleteProject(e, project.id)}
                            disabled={isDeleting === project.id}
                          >
                            <Trash2 className="w-4 h-4 mr-3" />
                            {isDeleting === project.id ? 'Deleting...' : 'Delete Project'}
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Status Badge */}
                  <div className="mb-4">
                    <StatusBadge status={project.status || 'Unknown'} />
                  </div>

                  {/* Project Description */}
                  {project.description && (
                    <p className="text-sm text-slate-600 mb-4 line-clamp-2">
                      {project.description}
                    </p>
                  )}

                  {/* Project Details Grid */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-slate-500 font-medium">Building Consent</span>
                      <p className="text-slate-700 mt-1">{project.building_consent || '-'}</p>
                    </div>
                    <div>
                      <span className="text-slate-500 font-medium">Resource Consent</span>
                      <p className="text-slate-700 mt-1">{project.resource_consent || '-'}</p>
                    </div>
                    <div>
                      <span className="text-slate-500 font-medium">Start Date</span>
                      <p className="text-slate-700 mt-1">{formatDate(project.start_date)}</p>
                    </div>
                    <div>
                      <span className="text-slate-500 font-medium">Completion</span>
                      <p className="text-slate-700 mt-1">{formatDate(project.completion_date)}</p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        /* List View */
        <div className="space-y-3">
          {sortedProjects.map((project, index) => {
            // Get color based on project name
            const projectColor = getProjectColor(project.name || '');

            return (
              <div
                key={project.id}
                className="group relative bg-white rounded-lg border border-slate-300 shadow-sm hover:shadow-md hover:bg-blue-50/50 hover:border-blue-300 transition-all duration-300 cursor-pointer overflow-hidden"
                onClick={() => handleRowClick(project.id)}
              >
                {/* Horizontal Card Layout */}
                <div className="flex items-center p-5" style={{ width: '95%' }}>
                  {/* Left side - Project Info */}
                  <div className="flex items-center space-x-4 flex-1 min-w-0">
                    <div
                      className="w-14 h-14 rounded-xl flex items-center justify-center shadow-md flex-shrink-0"
                      style={{
                        background: `linear-gradient(135deg, ${projectColor.bg} 0%, ${projectColor.bg}dd 100%)`,
                        color: projectColor.text
                      }}
                    >
                      <span className="text-xl font-bold">
                        {(project.name || '').charAt(0).toUpperCase()}
                      </span>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-slate-900 text-lg truncate group-hover:text-violet-600 transition-colors">
                          {project.name || 'Unnamed Project'}
                        </h3>
                        <StatusBadge status={project.status || 'Unknown'} />
                      </div>
                      <div className="flex items-center text-sm text-slate-500 mb-1">
                        <LocationIcon className="w-4 h-4 mr-1.5 flex-shrink-0" />
                        <span className="truncate">{project.location || 'No location'}</span>
                      </div>
                      {project.description && (
                        <p className="text-sm text-slate-600 line-clamp-1">
                          {project.description}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Middle - Project Details */}
                  <div className="hidden lg:flex items-center space-x-8 text-sm">
                    <div className="text-center">
                      <span className="text-slate-500 font-medium block">Building Consent</span>
                      <p className="text-slate-700 mt-1">{project.building_consent || '-'}</p>
                    </div>
                    <div className="text-center">
                      <span className="text-slate-500 font-medium block">Resource Consent</span>
                      <p className="text-slate-700 mt-1">{project.resource_consent || '-'}</p>
                    </div>
                    <div className="text-center">
                      <span className="text-slate-500 font-medium block">Start Date</span>
                      <p className="text-slate-700 mt-1">{formatDate(project.start_date)}</p>
                    </div>
                    <div className="text-center">
                      <span className="text-slate-500 font-medium block">Completion</span>
                      <p className="text-slate-700 mt-1">{formatDate(project.completion_date)}</p>
                    </div>
                  </div>

                  {/* Right side - Actions */}
                  <div className="relative ml-4" onClick={(e) => e.stopPropagation()}>
                    <button
                      className="p-2 rounded-lg text-slate-400 hover:text-slate-600 hover:bg-slate-100 transition-all opacity-0 group-hover:opacity-100"
                      onClick={(e) => toggleMenu(e, project.id)}
                    >
                      <MoreVertical className="w-5 h-5" />
                    </button>

                    {/* Action Menu */}
                    {activeMenu === project.id && (
                      <div ref={menuRef} className="absolute top-full right-0 mt-2 z-30 bg-white rounded-lg shadow-xl py-2 min-w-[180px] border border-slate-300">
                        <Link
                          href={`/projects/${project.id}`}
                          className="flex items-center px-4 py-2.5 text-sm text-slate-700 hover:bg-slate-50 hover:text-violet-600 w-full text-left transition-colors"
                        >
                          <ExternalLink className="w-4 h-4 mr-3" />
                          View Project
                        </Link>
                        <button
                          className="flex items-center px-4 py-2.5 text-sm text-slate-700 hover:bg-slate-50 hover:text-violet-600 w-full text-left transition-colors"
                          onClick={(e) => handleArchiveProject(e, project.id, project)}
                          disabled={isArchiving === project.id}
                        >
                          <Archive className="w-4 h-4 mr-3" />
                          {isArchiving === project.id ? 'Archiving...' : 'Archive Project'}
                        </button>
                        <div className="border-t border-slate-100 my-1"></div>
                        <button
                          className="flex items-center px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 w-full text-left transition-colors"
                          onClick={(e) => handleDeleteProject(e, project.id)}
                          disabled={isDeleting === project.id}
                        >
                          <Trash2 className="w-4 h-4 mr-3" />
                          {isDeleting === project.id ? 'Deleting...' : 'Delete Project'}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Empty State */}
      {sortedProjects.length === 0 && (
        <div className="text-center py-20 bg-white rounded-lg border border-slate-300 shadow-sm">
          <div className="w-24 h-24 mx-auto mb-6 rounded-lg bg-gradient-to-br from-violet-100 to-purple-100 flex items-center justify-center shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-violet-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h3 className="text-2xl font-semibold text-slate-900 mb-3">No projects found</h3>
          <p className="text-slate-600 max-w-md mx-auto mb-8 text-lg">Create a new project to get started or try adjusting your search criteria.</p>
          <button className="px-6 py-3 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-xl font-medium shadow-lg hover:from-violet-600 hover:to-purple-700 transition-all transform hover:scale-105">
            Create New Project
          </button>
        </div>
      )}
    </div>
  );
}

// Status badge component
function StatusBadge({ status }: { status: string }) {
  let bgColor = '';
  let textColor = '';
  let dotColor = '';

  switch (status) {
    case 'In Progress':
      bgColor = 'bg-blue-50';
      textColor = 'text-blue-700';
      dotColor = 'bg-blue-500';
      break;
    case 'Planning':
      bgColor = 'bg-amber-50';
      textColor = 'text-amber-700';
      dotColor = 'bg-amber-500';
      break;
    case 'Completed':
      bgColor = 'bg-emerald-50';
      textColor = 'text-emerald-700';
      dotColor = 'bg-emerald-500';
      break;
    case 'Archived':
      bgColor = 'bg-slate-50';
      textColor = 'text-slate-600';
      dotColor = 'bg-slate-400';
      break;
    case 'On Hold':
      bgColor = 'bg-orange-50';
      textColor = 'text-orange-700';
      dotColor = 'bg-orange-500';
      break;
    default:
      bgColor = 'bg-violet-50';
      textColor = 'text-violet-700';
      dotColor = 'bg-violet-500';
  }

  return (
    <span className={`inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-medium ${bgColor} ${textColor}`}>
      <span className={`w-2 h-2 rounded-full mr-2 ${dotColor}`}></span>
      {status}
    </span>
  );
}

// Location icon component
function LocationIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
      <circle cx="12" cy="10" r="3"></circle>
    </svg>
  );
}
